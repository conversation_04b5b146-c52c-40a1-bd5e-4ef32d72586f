{
	"root": true,

	"extends": "@ljharb/eslint-config/node/0.4",

	"rules": {
		"array-element-newline": 0,
		"complexity": 0,
		"func-style": [2, "declaration"],
		"max-lines-per-function": 0,
		"max-nested-callbacks": 1,
		"max-statements-per-line": 1,
		"max-statements": 0,
		"multiline-comment-style": 0,
		"no-continue": 1,
		"no-param-reassign": 1,
		"no-restricted-syntax": 1,
		"object-curly-newline": 0,
	},

	"overrides": [
		{
			"files": "test/**",
			"rules": {
				"camelcase": 0,
			},
		},
	]
}
