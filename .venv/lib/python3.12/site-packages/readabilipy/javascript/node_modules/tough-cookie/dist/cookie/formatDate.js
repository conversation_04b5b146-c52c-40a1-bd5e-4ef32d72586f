"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatDate = formatDate;
/**
 * Format a {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date | Date} into
 * the {@link https://www.rfc-editor.org/rfc/rfc2616#section-3.3.1 | preferred Internet standard format}
 * defined in {@link https://www.rfc-editor.org/rfc/rfc822#section-5 | RFC822} and
 * updated in {@link https://www.rfc-editor.org/rfc/rfc1123#page-55 | RFC1123}.
 *
 * @example
 * ```
 * formatDate(new Date(0)) === 'Thu, 01 Jan 1970 00:00:00 GMT`
 * ```
 *
 * @param date - the date value to format
 * @public
 */
function formatDate(date) {
    return date.toUTCString();
}
